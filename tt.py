import geoip2.database
import os
import logging
import ipaddress
import re
import urllib.parse
from collections import defaultdict

IP_INDEX = "ips.txt"
# IP_INDEX = "ip.txt"
# 全局变量：选择生成的URI类型
# 0 = 两者都生成, 1 = 只生成 Vless, 2 = 只生成 Trojan
GENERATE_TYPE = 1

# 全局变量：自定义 proxyip，可选设置
PROXY_IP = None
# PROXY_IP = "*************"

# 根据 PROXY_IP 的值设置 CUSTOM_PATH
if PROXY_IP:
    CUSTOM_PATH_A = f"/?ed=2560&relayip={PROXY_IP}"
    CUSTOM_PATH_Z = f"/trws?ed=2560&relayip={PROXY_IP}"
else:
    CUSTOM_PATH_A = "/?ed=2560"
    CUSTOM_PATH_Z = "/trws?ed=2560"


def url_encode_path(path):
    """将路径字符串转换为 URL 编码格式。"""
    return urllib.parse.quote(path)


def main():
    # 获取当前工作目录
    cwd = os.getcwd()
    print(f"当前工作目录: {cwd}")

    # 定义Vless URI字符串
    vless_uri = "vless://4900c7d9-aa22-4a6d-ad15-93856997d667@************:443?encryption=none&security=tls&sni=nds.ruaex126.linkpc.net&fp=randomized&type=ws&host=nds.ruaex126.linkpc.net&path=%2F%3Fed%3D2560%26proxyip%3D172.104.191.203#4-SG-AS63949"

    # 定义Trojan URI字符串
    trojan_uri = "trojan://a233255z@*********:443?security=tls&sni=nds.ruaex126.linkpc.net&fp=randomized&type=ws&host=nds.ruaex126.linkpc.net&path=%2Ftr%3Fed%3D2560%26proxyip%3D172.86.82.60#1-SG-AS18464"

    # 定义Vless URI字符串
    vless_uri = "vless://<EMAIL>:443?encryption=none&security=tls&sni=3ds.almain126.changeip.biz&fp=chrome&type=ws&host=3ds.almain126.changeip.biz&path=%2F%3Fed%3D2560%26relayip%3D*************#2-SG-AS63949"

    # 定义Trojan URI字符串
    trojan_uri = "trojan://a233255z@*********:443?security=tls&sni=3ds.ruaex126.linkpc.net&fp=randomized&type=ws&host=3ds.ruaex126.linkpc.net&path=%2Ftr%3Fed%3D2560%26proxyip%3D172.86.82.60#1-SG-AS18464"

    # 从Vless URI中提取uuid和domain
    vlessuuid_match = re.search(r"vless://([^@]+)@", vless_uri)
    vlessdomain_match = re.search(r"sni=([^&]+)", vless_uri)

    if vlessuuid_match and vlessdomain_match:
        vlessuuid = vlessuuid_match.group(1)
        vlessdomain = vlessdomain_match.group(1)
    else:
        logging.error("无法从Vless URI中提取uuid或domain")
        return

    print(f"提取的vless uuid: {vlessuuid}")
    print(f"提取的vless domain: {vlessdomain}")

    # 从Trojan URI中提取password和domain
    trojanpassword_match = re.search(r"trojan://([^@]+)@", trojan_uri)
    trojandomain_match = re.search(r"sni=([^&]+)", trojan_uri)

    if trojanpassword_match and trojandomain_match:
        trojanpassword = trojanpassword_match.group(1)
        trojandomain = trojandomain_match.group(1)
    else:
        logging.error("无法从Trojan URI中提取password或domain")
        return

    print(f"提取的trojan password: {trojanpassword}")
    print(f"提取的trojan domain: {trojandomain}")

    default_port = 443  # 设置默认端口 trojan.fxxk.cloudns.org

    # 检查必要的文件是否存在
    required_files = [f"{IP_INDEX}", "GeoLite2-Country.mmdb", "GeoLite2-ASN.mmdb"]
    for file in required_files:
        if not os.path.exists(file):
            logging.error(f"错误: 找不到文件 {file}")
            return

    # 初始化GeoIP2读取器
    with (
        geoip2.database.Reader("GeoLite2-Country.mmdb") as country_reader,
        geoip2.database.Reader("GeoLite2-ASN.mmdb") as asn_reader,
    ):
        # 对 CUSTOM_PATH 进行 URL 编码
        encoded_path_a = url_encode_path(CUSTOM_PATH_A)
        encoded_path_z = url_encode_path(CUSTOM_PATH_Z)

        # 定义Vless URI模板 (使用 f-string)
        vless_template = f"vless://{vlessuuid}@{{ip}}:{{port}}?encryption=none&security=tls&sni={vlessdomain}&fp=randomized&type=ws&host={vlessdomain}&path={encoded_path_a}#{{remark}}"

        # 定义Trojan URI模板 (使用 f-string)
        trojan_template = f"trojan://{trojanpassword}@{{ip}}:{{port}}?security=tls&sni={trojandomain}&fp=randomized&type=ws&host={trojandomain}&path={encoded_path_z}#{{remark}}"

        # 用于存储AS信息的字典
        as_info = defaultdict(lambda: {"count": 0, "ips": []})

        # 首先读取文件并收集AS信息
        try:
            with open(f"{IP_INDEX}", "r") as file:
                for line in file:
                    ip_and_port = line.strip()

                    # 首先检查是否为空格分隔格式 (例如: ******* 80, 2001:db8::1 80, example.com 443)
                    if " " in ip_and_port and not ip_and_port.startswith("["):
                        space_parts = ip_and_port.split()
                        if len(space_parts) >= 2 and space_parts[1].isdigit():
                            ip = space_parts[0]
                            port = int(space_parts[1])
                        else:
                            # 如果不是有效的空格分隔格式，继续使用原有逻辑
                            ip = space_parts[0]
                            port = default_port
                    # 处理可能的IPv6地址
                    elif ip_and_port.startswith("["):
                        # IPv6 地址 - 支持 [IPv6]:port 和 [IPv6] port 格式
                        ip_end = ip_and_port.index("]")
                        ip = ip_and_port[1:ip_end]
                        port_part = ip_and_port[ip_end + 1 :].strip()

                        if port_part.startswith(" ") and port_part.strip().isdigit():
                            # 空格分隔格式: [2001:db8::1] 80
                            port = int(port_part.strip())
                        elif port_part.startswith(":"):
                            # 冒号分隔格式: [2001:db8::1]:80
                            port = (
                                int(port_part.split(":")[1])
                                if ":" in port_part
                                else default_port
                            )
                        else:
                            port = default_port
                    else:
                        # IPv4 地址或不带端口的IPv6地址
                        ip_parts = ip_and_port.split(":")
                        if len(ip_parts) > 2:  # 可能是不带方括号的IPv6地址
                            ip = (
                                ":".join(ip_parts[:-1])
                                if ip_parts[-1].isdigit()
                                else ip_and_port
                            )
                            port = (
                                int(ip_parts[-1])
                                if ip_parts[-1].isdigit()
                                else default_port
                            )
                        else:
                            ip = ip_parts[0]
                            port = (
                                int(ip_parts[1]) if len(ip_parts) == 2 else default_port
                            )

                    try:
                        # 尝试验证是否为IP地址
                        is_ip_address = True
                        try:
                            ipaddress.ip_address(ip)
                        except ValueError:
                            # 不是有效的IP地址，可能是域名
                            is_ip_address = False

                        if is_ip_address:
                            # 对IP地址进行GeoIP查询
                            try:
                                country_response = country_reader.country(ip)
                                country_code = (
                                    country_response.country.iso_code or "Unknown"
                                )
                                asn_response = asn_reader.asn(ip)
                                asn_number = asn_response.autonomous_system_number or 0
                            except geoip2.errors.AddressNotFoundError:
                                country_code = "Unknown"
                                asn_number = 0
                        else:
                            # 对域名使用默认值
                            country_code = "Domain"
                            asn_number = 0

                        as_key = f"{country_code}-AS{asn_number}"
                        as_info[as_key]["count"] += 1
                        as_info[as_key]["ips"].append((ip, port))  # 存储IP和端口的元组
                    except Exception as e:
                        logging.error(f"处理地址 {ip} 时出错: {str(e)}")
        except IOError as e:
            logging.fatal(f"无法打开输入文件: {e}")
            return

        # 生成选择的URI
        vless_uris = []
        trojan_uris = []
        for as_key, info in as_info.items():
            for i, (ip, port) in enumerate(info["ips"], 1):
                try:
                    # 检查是否为IPv6地址，如果是，则用方括号括起来
                    formatted_ip = f"[{ip}]" if ":" in ip else ip
                    # 根据GENERATE_TYPE生成相应的URI
                    if GENERATE_TYPE in [0, 1]:
                        vless_uri = vless_template.format(
                            ip=formatted_ip,
                            port=port,
                            remark=f"{i}-{as_key}",
                        )
                        vless_uris.append(vless_uri)
                    if GENERATE_TYPE in [0, 2]:
                        trojan_uri = trojan_template.format(
                            ip=formatted_ip,
                            port=port,
                            remark=f"{i}-{as_key}",
                        )
                        trojan_uris.append(trojan_uri)
                except Exception as e:
                    logging.error(f"为IP {ip}:{port} 生成URI时出错: {str(e)}")

        # 打印和保存生成的URIs
        if GENERATE_TYPE in [0, 1]:
            # 对Vless URIs进行排序
            vless_uris.sort()
            print("Vless URIs:")
            for uri in vless_uris:
                print(uri)
            try:
                with open("vless_uris.txt", "w") as vless_file:
                    for uri in vless_uris:
                        vless_file.write(f"{uri}\n")
                print("Vless URIs 已保存到 vless_uris.txt")
            except IOError as e:
                logging.error(f"无法创建Vless输出文件: {e}")

        if GENERATE_TYPE in [0, 2]:
            # 对Trojan URIs进行排序
            trojan_uris.sort()
            print("\nTrojan URIs:")
            for uri in trojan_uris:
                print(uri)
            try:
                with open("trojan_uris.txt", "w") as trojan_file:
                    for uri in trojan_uris:
                        trojan_file.write(f"{uri}\n")
                print("Trojan URIs 已保存到 trojan_uris.txt")
            except IOError as e:
                logging.error(f"无法创建Trojan输出文件: {e}")


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    main()
